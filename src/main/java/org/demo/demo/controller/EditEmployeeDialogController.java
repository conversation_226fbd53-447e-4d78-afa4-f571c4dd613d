package org.demo.demo.controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.demo.demo.entities.Utilisateur;
import org.demo.demo.services.EmployeeService;

/**
 * Contrôleur pour la boîte de dialogue d'édition d'employé
 */
public class EditEmployeeDialogController {

    @FXML
    private Label currentUsernameLabel;

    @FXML
    private TextField newUsernameField;

    @FXML
    private PasswordField newPasswordField;

    @FXML
    private Label errorLabel;

    @FXML
    private Button saveButton;

    @FXML
    private Button cancelButton;

    private EmployeeService employeeService;
    private Utilisateur currentEmployee;
    private boolean saveClicked = false;

    /**
     * Initialise la boîte de dialogue avec les données de l'employé
     */
    public void setEmployeeData(Utilisateur employee, EmployeeService employeeService) {
        this.currentEmployee = employee;
        this.employeeService = employeeService;
        
        // Afficher le nom d'utilisateur actuel
        currentUsernameLabel.setText(employee.getUsername());
        
        // Effacer les messages d'erreur
        errorLabel.setText("");
    }

    /**
     * Gère le clic sur le bouton Sauvegarder
     */
    @FXML
    private void handleSave() {
        String newUsername = newUsernameField.getText().trim();
        String newPassword = newPasswordField.getText();
        
        // Vérifier qu'au moins un champ est rempli
        if (newUsername.isEmpty() && newPassword.isEmpty()) {
            showError("Veuillez remplir au moins un champ pour effectuer une modification.");
            return;
        }
        
        try {
            boolean success = false;
            
            // Si les deux champs sont remplis, utiliser updateEmployee
            if (!newUsername.isEmpty() && !newPassword.isEmpty()) {
                success = employeeService.updateEmployee(currentEmployee.getUsername(), newUsername, newPassword);
            }
            // Si seulement le nom d'utilisateur est modifié
            else if (!newUsername.isEmpty() && newPassword.isEmpty()) {
                // Créer une méthode pour mettre à jour seulement le nom d'utilisateur
                success = updateUsernameOnly(newUsername);
            }
            // Si seulement le mot de passe est modifié
            else if (newUsername.isEmpty() && !newPassword.isEmpty()) {
                success = employeeService.updateEmployeePassword(currentEmployee.getUsername(), newPassword);
            }
            
            if (success) {
                saveClicked = true;
                closeDialog();
            } else {
                showError("Erreur lors de la mise à jour de l'employé.");
            }
            
        } catch (IllegalArgumentException e) {
            showError(e.getMessage());
        } catch (Exception e) {
            showError("Erreur inattendue: " + e.getMessage());
        }
    }

    /**
     * Met à jour seulement le nom d'utilisateur en conservant le mot de passe actuel
     */
    private boolean updateUsernameOnly(String newUsername) {
        // Validation du format email
        if (!newUsername.matches("^[A-Za-z0-9._%+-]+@capgemini\\.com$")) {
            throw new IllegalArgumentException("L'adresse e-mail doit se terminer par @capgemini.com");
        }
        
        // Vérifier que le nouveau nom n'existe pas déjà
        if (!employeeService.isUsernameAvailable(newUsername)) {
            throw new IllegalArgumentException("Un utilisateur avec ce nom existe déjà");
        }
        
        // Mettre à jour seulement le nom d'utilisateur
        currentEmployee.setUsername(newUsername);
        return employeeService.updateEmployeeUsername(currentEmployee.getUsername(), newUsername);
    }

    /**
     * Gère le clic sur le bouton Annuler
     */
    @FXML
    private void handleCancel() {
        closeDialog();
    }

    /**
     * Affiche un message d'erreur
     */
    private void showError(String message) {
        errorLabel.setText(message);
    }

    /**
     * Ferme la boîte de dialogue
     */
    private void closeDialog() {
        Stage stage = (Stage) saveButton.getScene().getWindow();
        stage.close();
    }

    /**
     * Retourne true si l'utilisateur a cliqué sur Sauvegarder
     */
    public boolean isSaveClicked() {
        return saveClicked;
    }
}
