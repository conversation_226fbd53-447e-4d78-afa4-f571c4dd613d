package org.demo.demo.controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.Alert.AlertType;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.geometry.Insets;
import org.demo.demo.dao.UtilisateurDAO;
import org.demo.demo.entities.Utilisateur;
import org.demo.demo.config.DatabaseUtil;
import org.demo.demo.services.EmployeeService;

import java.sql.Connection;
import java.util.List;
import java.util.Optional;

public class ManageEmployeesController {

    @FXML
    private TextField usernameField;

    @FXML
    private PasswordField passwordField;

    @FXML
    private Label statusLabel;

    @FXML
    private VBox employeeContainer;

    private EmployeeService employeeService;

    @FXML
    public void initialize() {
        try {
            Connection conn = DatabaseUtil.getConnection();
            UtilisateurDAO utilisateurDAO = new UtilisateurDAO(conn);
            employeeService = new EmployeeService(utilisateurDAO);
            refreshEmployeeList();
        } catch (Exception e) {
            statusLabel.setText("Erreur de connexion à la base de données");
        }
    }

    @FXML
    private void handleAddEmployee() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();

        if (username.isEmpty() || password.isEmpty()) {
            statusLabel.setText("Veuillez remplir tous les champs !");
            return;
        }

        try {
            // Utiliser le service pour ajouter l'employé
            boolean success = employeeService.addEmployee(username, password);

            if(success) {
                statusLabel.setText("Employé ajouté avec succès !");
                usernameField.clear();
                passwordField.clear();
                refreshEmployeeList();
            } else {
                statusLabel.setText("Erreur lors de l'ajout de l'employé !");
            }

        } catch (IllegalArgumentException e) {
            statusLabel.setText(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de l'ajout de l'employé !");
        }
    }

    private void refreshEmployeeList() {
        try {
            List<Utilisateur> users = employeeService.getAllEmployees();
            employeeContainer.getChildren().clear();

            for (Utilisateur user : users) {
                HBox employeeRow = createEmployeeRow(user);
                employeeContainer.getChildren().add(employeeRow);
            }
        } catch (Exception e) {
            statusLabel.setText("Erreur lors du chargement de la liste !");
        }
    }

    private HBox createEmployeeRow(Utilisateur user) {
        HBox row = new HBox();
        row.getStyleClass().add("employee-row");
        row.setSpacing(10);
        row.setPadding(new Insets(5, 10, 5, 10));

        // Nom de l'employé
        Label nameLabel = new Label(user.getUsername());
        nameLabel.getStyleClass().add("employee-name");

        // Spacer pour pousser les boutons à droite
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        // Bouton modifier
        Button editBtn = new Button("Modifier");
        editBtn.getStyleClass().add("action-button");
        editBtn.setTooltip(new Tooltip("Modifier le mot de passe"));
        editBtn.setOnAction(e -> handleEditEmployee(user.getUsername()));

        // Bouton supprimer
        Button deleteBtn = new Button("Supprimer");
        deleteBtn.getStyleClass().add("action-button");
        deleteBtn.setTooltip(new Tooltip("Supprimer l'employé"));
        deleteBtn.setOnAction(e -> handleDeleteEmployee(user.getUsername()));

        row.getChildren().addAll(nameLabel, spacer, editBtn, deleteBtn);
        return row;
    }

    private void handleEditEmployee(String username) {
        // Créer un menu de choix pour l'utilisateur
        Alert choiceAlert = new Alert(Alert.AlertType.CONFIRMATION);
        choiceAlert.setTitle("Modifier l'employé");
        choiceAlert.setHeaderText("Que souhaitez-vous modifier pour : " + username + " ?");
        choiceAlert.setContentText("Choisissez une option:");

        ButtonType passwordBtn = new ButtonType("Mot de passe seulement");
        ButtonType usernameBtn = new ButtonType("Nom d'utilisateur seulement");
        ButtonType bothBtn = new ButtonType("Les deux");
        ButtonType cancelBtn = new ButtonType("Annuler", ButtonBar.ButtonData.CANCEL_CLOSE);

        choiceAlert.getButtonTypes().setAll(passwordBtn, usernameBtn, bothBtn, cancelBtn);

        Optional<ButtonType> result = choiceAlert.showAndWait();

        if (result.isPresent()) {
            if (result.get() == passwordBtn) {
                handlePasswordChange(username);
            } else if (result.get() == usernameBtn) {
                handleUsernameChange(username);
            } else if (result.get() == bothBtn) {
                handleBothChange(username);
            }
        }
    }

    private void handlePasswordChange(String username) {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Modifier le mot de passe");
        dialog.setHeaderText("Modifier le mot de passe de : " + username);
        dialog.setContentText("Nouveau mot de passe:");

        dialog.showAndWait().ifPresent(newPassword -> {
            if (newPassword.trim().isEmpty()) {
                statusLabel.setText("Le mot de passe ne peut pas être vide !");
                return;
            }

            try {
                boolean success = employeeService.updateEmployeePassword(username, newPassword);
                if (success) {
                    statusLabel.setText("Mot de passe modifié avec succès !");
                } else {
                    statusLabel.setText("Erreur lors de la modification !");
                }
            } catch (Exception e) {
                statusLabel.setText("Erreur: " + e.getMessage());
            }
        });
    }

    private void handleUsernameChange(String currentUsername) {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Modifier le nom d'utilisateur");
        dialog.setHeaderText("Modifier le nom d'utilisateur de : " + currentUsername);
        dialog.setContentText("Nouveau nom d'utilisateur:");

        dialog.showAndWait().ifPresent(newUsername -> {
            if (newUsername.trim().isEmpty()) {
                statusLabel.setText("Le nom d'utilisateur ne peut pas être vide !");
                return;
            }

            // Validation du format email
            if (!newUsername.matches("^[A-Za-z0-9._%+-]+@capgemini\\.com$")) {
                statusLabel.setText("L'adresse e-mail doit se terminer par @capgemini.com");
                return;
            }

            try {
                // Utiliser la méthode updateEmployee avec le même mot de passe
                Optional<Utilisateur> userOpt = employeeService.findEmployeeByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    Utilisateur user = userOpt.get();
                    // Créer un mot de passe temporaire pour la mise à jour
                    // On ne peut pas récupérer le mot de passe original, donc on demande à l'admin de le saisir
                    TextInputDialog passwordDialog = new TextInputDialog();
                    passwordDialog.setTitle("Mot de passe requis");
                    passwordDialog.setHeaderText("Pour changer le nom d'utilisateur, veuillez saisir un nouveau mot de passe");
                    passwordDialog.setContentText("Nouveau mot de passe:");

                    passwordDialog.showAndWait().ifPresent(newPassword -> {
                        if (newPassword.trim().isEmpty()) {
                            statusLabel.setText("Le mot de passe ne peut pas être vide !");
                            return;
                        }

                        try {
                            boolean success = employeeService.updateEmployee(currentUsername, newUsername, newPassword);
                            if (success) {
                                statusLabel.setText("Nom d'utilisateur et mot de passe modifiés avec succès !");
                                refreshEmployeeList();
                            } else {
                                statusLabel.setText("Erreur lors de la modification !");
                            }
                        } catch (Exception e) {
                            statusLabel.setText("Erreur: " + e.getMessage());
                        }
                    });
                }
            } catch (Exception e) {
                statusLabel.setText("Erreur: " + e.getMessage());
            }
        });
    }

    private void handleBothChange(String currentUsername) {
        // Demander le nouveau nom d'utilisateur
        TextInputDialog usernameDialog = new TextInputDialog();
        usernameDialog.setTitle("Modifier l'employé");
        usernameDialog.setHeaderText("Modifier les informations de : " + currentUsername);
        usernameDialog.setContentText("Nouveau nom d'utilisateur:");

        usernameDialog.showAndWait().ifPresent(newUsername -> {
            if (newUsername.trim().isEmpty()) {
                statusLabel.setText("Le nom d'utilisateur ne peut pas être vide !");
                return;
            }

            // Validation du format email
            if (!newUsername.matches("^[A-Za-z0-9._%+-]+@capgemini\\.com$")) {
                statusLabel.setText("L'adresse e-mail doit se terminer par @capgemini.com");
                return;
            }

            // Demander le nouveau mot de passe
            TextInputDialog passwordDialog = new TextInputDialog();
            passwordDialog.setTitle("Modifier le mot de passe");
            passwordDialog.setHeaderText("Nouveau mot de passe pour : " + newUsername);
            passwordDialog.setContentText("Nouveau mot de passe:");

            passwordDialog.showAndWait().ifPresent(newPassword -> {
                if (newPassword.trim().isEmpty()) {
                    statusLabel.setText("Le mot de passe ne peut pas être vide !");
                    return;
                }

                try {
                    boolean success = employeeService.updateEmployee(currentUsername, newUsername, newPassword);
                    if (success) {
                        statusLabel.setText("Employé modifié avec succès !");
                        refreshEmployeeList();
                    } else {
                        statusLabel.setText("Erreur lors de la modification !");
                    }
                } catch (Exception e) {
                    statusLabel.setText("Erreur: " + e.getMessage());
                }
            });
        });
    }

    private void handleDeleteEmployee(String username) {
        // Confirmation de suppression
        Alert confirmAlert = new Alert(AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirmer la suppression");
        confirmAlert.setHeaderText("Supprimer l'employé");
        confirmAlert.setContentText("Êtes-vous sûr de vouloir supprimer l'employé : " + username + " ?");

        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    boolean success = employeeService.deleteEmployeeByUsername(username);
                    if (success) {
                        statusLabel.setText("Employé supprimé avec succès !");
                        refreshEmployeeList();
                    } else {
                        statusLabel.setText("Erreur lors de la suppression !");
                    }
                } catch (Exception e) {
                    statusLabel.setText("Erreur: " + e.getMessage());
                }
            }
        });
    }
}
