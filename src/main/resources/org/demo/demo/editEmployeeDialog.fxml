<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="org.demo.demo.controller.EditEmployeeDialogController">
   <children>
      <Label text="Modifier l'employé" styleClass="dialog-title" />
      
      <VBox spacing="15" styleClass="dialog-content">
         <VBox spacing="5">
            <Label text="Nom d'utilisateur actuel:" styleClass="field-label" />
            <Label fx:id="currentUsernameLabel" styleClass="current-value" />
         </VBox>
         
         <VBox spacing="5">
            <Label text="Nouveau nom d'utilisateur (optionnel):" styleClass="field-label" />
            <TextField fx:id="newUsernameField" promptText="<EMAIL>" />
            <Label text="Laissez vide pour conserver le nom actuel" styleClass="help-text" />
         </VBox>
         
         <VBox spacing="5">
            <Label text="Nouveau mot de passe (optionnel):" styleClass="field-label" />
            <PasswordField fx:id="newPasswordField" promptText="Nouveau mot de passe" />
            <Label text="Laissez vide pour conserver le mot de passe actuel" styleClass="help-text" />
         </VBox>
         
         <Label fx:id="errorLabel" text="" styleClass="error-message" />
      </VBox>
      
      <HBox spacing="10" styleClass="dialog-buttons">
         <Button fx:id="saveButton" text="Sauvegarder" onAction="#handleSave" styleClass="primary-button" />
         <Button fx:id="cancelButton" text="Annuler" onAction="#handleCancel" styleClass="secondary-button" />
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   <stylesheets>
      <URL value="@editEmployeeDialog.css" />
   </stylesheets>
</VBox>
