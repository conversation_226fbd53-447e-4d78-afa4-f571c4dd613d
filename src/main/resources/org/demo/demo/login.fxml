<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.image.*?>

<BorderPane xmlns:fx="http://javafx.com/fxml" fx:controller="org.demo.demo.controller.LoginController" stylesheets="@css/login.css" styleClass="login-root">

    <!-- Panneau de gauche avec branding -->
    <left>
        <VBox styleClass="branding-panel" alignment="CENTER" spacing="30">
            <padding>
                <Insets top="60" right="40" bottom="60" left="40"/>
            </padding>

            <!-- Logo et titre -->
            <VBox alignment="CENTER" spacing="20">
                <Circle radius="50" styleClass="logo-circle" fill="#4A90E2"/>
                <Label text="KitChiffre" styleClass="brand-title"/>
                <Label text="Solution de chiffrage d'entreprise" styleClass="brand-subtitle"/>
            </VBox>

            <!-- Fonctionnalités -->
            <VBox spacing="25" styleClass="features-list">
                <HBox spacing="15" alignment="CENTER_LEFT">
                    <Circle radius="4" styleClass="feature-dot"/>
                    <Label text="Gestion sécurisée des données" styleClass="feature-text"/>
                </HBox>
                <HBox spacing="15" alignment="CENTER_LEFT">
                    <Circle radius="4" styleClass="feature-dot"/>
                    <Label text="Interface intuitive et moderne" styleClass="feature-text"/>
                </HBox>
                <HBox spacing="15" alignment="CENTER_LEFT">
                    <Circle radius="4" styleClass="feature-dot"/>
                    <Label text="Collaboration d'équipe efficace" styleClass="feature-text"/>
                </HBox>
            </VBox>
        </VBox>
    </left>

    <!-- Panneau de droite avec formulaire de connexion -->
    <center>
        <VBox styleClass="login-panel" alignment="CENTER" spacing="0">
            <padding>
                <Insets top="80" right="60" bottom="80" left="60"/>
            </padding>

            <!-- En-tête du formulaire -->
            <VBox alignment="CENTER" spacing="10" styleClass="form-header">
                <Label text="Bienvenue" styleClass="welcome-title"/>
                <Label text="Connectez-vous à votre compte" styleClass="welcome-subtitle"/>
            </VBox>

            <!-- Formulaire de connexion -->
            <VBox spacing="25" styleClass="login-form">
                <VBox spacing="8">
                    <Label text="Adresse e-mail" styleClass="field-label"/>
                    <TextField fx:id="usernameField" promptText="<EMAIL>" styleClass="modern-input"/>
                </VBox>

                <VBox spacing="8">
                    <Label text="Mot de passe" styleClass="field-label"/>
                    <PasswordField fx:id="passwordField" promptText="Votre mot de passe" styleClass="modern-input"/>
                </VBox>

                <Button text="Se connecter" onAction="#handleLogin" styleClass="login-button"/>
            </VBox>

            <!-- Pied de page -->
            <VBox alignment="CENTER" spacing="10" styleClass="form-footer">
                <Label text="© 2024 Capgemini. Tous droits réservés." styleClass="copyright-text"/>
            </VBox>
        </VBox>
    </center>

</BorderPane>
