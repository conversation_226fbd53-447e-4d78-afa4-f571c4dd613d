<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns:fx="http://javafx.com/fxml" fx:controller="org.demo.demo.controller.LoginController"
      stylesheets="@css/login.css" styleClass="login-container"
      alignment="CENTER" spacing="30">

    <padding>
        <Insets top="100" right="50" bottom="100" left="50"/>
    </padding>

    <!-- En-tête -->
    <VBox alignment="CENTER" spacing="15" styleClass="header-section">
        <Label text="KitChiffre" styleClass="app-title"/>
        <Label text="Connexion" styleClass="login-title"/>
    </VBox>

    <!-- Formulaire de connexion -->
    <VBox spacing="20" styleClass="login-form" alignment="CENTER">
        <TextField fx:id="usernameField" promptText="<EMAIL>" styleClass="input-field"/>
        <PasswordField fx:id="passwordField" promptText="Mot de passe" styleClass="input-field"/>
        <Button text="Se connecter" onAction="#handleLogin" styleClass="login-btn"/>
    </VBox>

    <!-- Pied de page -->
    <Label text="© 2024 Capgemini" styleClass="footer-text"/>

</VBox>
