/* ======= Modern Enterprise Login Design ======= */

/* Root container */
.login-root {
    -fx-background-color: #f8fafc;
}

/* ======= Branding Panel (Left Side) ======= */
.branding-panel {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #4A90E2, #6BB6FF);
    -fx-min-width: 400;
    -fx-max-width: 400;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 20, 0, 5, 0);
}

.logo-circle {
    -fx-effect: dropshadow(gaussian, rgba(255, 255, 255, 0.3), 15, 0, 0, 0);
}

.brand-title {
    -fx-font-size: 36px;
    -fx-font-weight: 700;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 2, 0, 0, 1);
}

.brand-subtitle {
    -fx-font-size: 16px;
    -fx-font-weight: 400;
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-text-alignment: center;
}

.features-list {
    -fx-max-width: 300;
}

.feature-dot {
    -fx-fill: rgba(255, 255, 255, 0.8);
}

.feature-text {
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* ======= Login Panel (Right Side) ======= */
.login-panel {
    -fx-background-color: white;
    -fx-min-width: 490;
}

.form-header {
    -fx-padding: 0 0 40 0;
}

.welcome-title {
    -fx-font-size: 32px;
    -fx-font-weight: 700;
    -fx-text-fill: #2c3e50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.welcome-subtitle {
    -fx-font-size: 16px;
    -fx-font-weight: 400;
    -fx-text-fill: #7f8c8d;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* ======= Form Styles ======= */
.login-form {
    -fx-max-width: 350;
    -fx-min-width: 350;
    -fx-padding: 0 0 40 0;
}

.field-label {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #34495e;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-input {
    -fx-pref-width: 350;
    -fx-font-size: 16px;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-border-color: #e1e8ed;
    -fx-border-width: 2;
    -fx-padding: 16 20;
    -fx-background-color: #ffffff;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-prompt-text-fill: #95a5a6;
    -fx-text-fill: #2c3e50;
}

.modern-input:focused {
    -fx-border-color: #4A90E2;
    -fx-background-color: #ffffff;
    -fx-effect: dropshadow(gaussian, rgba(74, 144, 226, 0.2), 8, 0, 0, 0);
}

.login-button {
    -fx-background-color: linear-gradient(to bottom, #4A90E2, #6BB6FF);
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 16 0;
    -fx-background-radius: 8;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(74, 144, 226, 0.3), 10, 0, 0, 4);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-pref-width: 350;
}

.login-button:hover {
    -fx-background-color: linear-gradient(to bottom, #6BB6FF, #85C1E9);
    -fx-effect: dropshadow(gaussian, rgba(74, 144, 226, 0.4), 15, 0, 0, 6);
    -fx-scale-y: 1.02;
}

.login-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #3498db, #5dade2);
    -fx-scale-y: 0.98;
}

/* ======= Footer ======= */
.form-footer {
    -fx-padding: 20 0 0 0;
}

.copyright-text {
    -fx-font-size: 12px;
    -fx-font-weight: 400;
    -fx-text-fill: #95a5a6;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* ======= Responsive Design ======= */
@media (max-width: 1200px) {
    .branding-panel {
        -fx-min-width: 350;
        -fx-max-width: 350;
    }

    .login-panel {
        -fx-min-width: 450;
    }
}

/* ======= Animation Effects ======= */
.login-form {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 20, 0, 0, 10);
}

.modern-input {
    -fx-transition: all 0.3s ease;
}

.login-button {
    -fx-transition: all 0.2s ease;
}

.primary-button:pressed {
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
    -fx-effect: dropshadow(gaussian, rgba(41, 128, 185, 0.8), 8, 0, 0, 2);
}
