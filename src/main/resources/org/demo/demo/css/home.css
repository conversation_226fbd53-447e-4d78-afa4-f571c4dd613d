/* ======= Creative Home Design ======= */

/* Global Styles */
/* Fix pour les icônes FontIcon */
.font-icon {
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}
.scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
    -fx-hbar-policy: never;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
}

.scroll-pane .scroll-bar:vertical {
    -fx-opacity: 0.3;
}

.main-container {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8fafc, #e2e8f0);
    -fx-spacing: 0;
}


/* ======= Creative Features Section ======= */
.creative-features-section {
    -fx-padding: 30 20;
    -fx-background-color: white;
    -fx-alignment: center;
}

.section-header {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
}

.creative-section-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-section-subtitle {
    -fx-font-size: 14px;
    -fx-text-fill: #5A6C7D;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.section-underline {
    -fx-fill: linear-gradient(to right, #FF6B6B, #4ECDC4);
    -fx-arc-width: 1.5;
    -fx-arc-height: 1.5;
}

/* Modern Feature Cards */
.features-grid {
    -fx-max-width: 900;
    -fx-alignment: center;
    -fx-fill-width: false;
}

.modern-feature-card {
    -fx-background-color: white;
    -fx-padding: 15;
    -fx-background-radius: 10;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 10, 0.15, 0, 2);
    -fx-max-width: 180;
    -fx-min-width: 180;
    -fx-min-height: 220;
}

.modern-feature-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 25, 0.3, 0, 8);
    -fx-translate-y: -5;
}

.modern-icon-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 8, 0, 0, 2);
}

.modern-icon-bg {
    -fx-opacity: 1.0;
    -fx-fill: #4A90E2; /* Couleur par défaut au cas où le dégradé ne fonctionne pas */
}

.modern-bg-1 {
    -fx-fill: #2E86C1; /* Bleu foncé */
}

.modern-bg-2 {
    -fx-fill: #4A90E2; /* Bleu moyen */
}

.modern-bg-3 {
    -fx-fill: #5DADE2; /* Bleu clair */
}

/* Classes spécifiques pour forcer les couleurs */
.modern-icon-bg.modern-bg-1 {
    -fx-fill: #2E86C1 !important;
}

.modern-icon-bg.modern-bg-2 {
    -fx-fill: #4A90E2 !important;
}

.modern-icon-bg.modern-bg-3 {
    -fx-fill: #5DADE2 !important;
}

.modern-icon {
    -fx-icon-size: 28;
    -fx-icon-color: white;
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}

/* Couleurs spécifiques pour chaque icône */
.icon-blue-1 {
    -fx-icon-color: #4A90E2;
}

.icon-blue-2 {
    -fx-icon-color: #5DADE2;
}

.icon-blue-3 {
    -fx-icon-color: #3498DB;
}

.modern-feature-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-feature-description {
    -fx-font-size: 12px;
    -fx-text-fill: #5A6C7D;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 1.3;
}

.modern-feature-button {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-text-fill: #2C3E50;
    -fx-font-size: 11px;
    -fx-font-weight: 600;
    -fx-padding: 8 12;
    -fx-background-radius: 15;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 15;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-max-width: 150;
}

.modern-feature-button:hover {
    -fx-background-color:linear-gradient(from 0% 0% to 100% 100%, #4A90E2, #6BB6FF);
    -fx-text-fill: white;
    -fx-border-color: #6BB6FF;
}

/* ======= Creative Stats Dashboard ======= */
.stats-dashboard {
    -fx-padding: 40 30;
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8f9fa, #e9ecef);
    -fx-min-height: 150;
}

.stats-pattern {
    -fx-background-color: transparent;
}

.stats-container {
    -fx-max-width: 650;
    -fx-alignment: center;
}

.stat-card {
    -fx-background-color: white;
    -fx-padding: 18;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0.2, 0, 2);
    -fx-min-width: 110;
    -fx-max-width: 110;
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 20, 0.3, 0, 8);
    -fx-translate-y: -3;
}

.stat-icon-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 6, 0, 0, 2);
}

.stat-icon-bg {
    -fx-opacity: 1.0;
}

.stat-bg-1 {
    -fx-fill: linear-gradient(to bottom, #4A90E2, #6BB6FF);
}

.stat-bg-2 {
    -fx-fill: linear-gradient(to bottom, #5DADE2, #85C1E9);
}

.stat-bg-3 {
    -fx-fill: linear-gradient(to bottom, #3498DB, #5DADE2);
}

.stat-bg-4 {
    -fx-fill: linear-gradient(to bottom, #2E86C1, #4A90E2);
}

.stat-icon {
    -fx-icon-size: 16;
    -fx-icon-color: white;
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}

.creative-stat-number {
    -fx-font-size: 22px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-stat-label {
    -fx-font-size: 11px;
    -fx-text-fill: #5A6C7D;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}
.hero-icon {
    -fx-font-size: 28px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 1, 0, 0, 1);
    -fx-font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}
.search-hero-icon {
    -fx-font-size: 28px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 1, 0, 0, 1);
    -fx-font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}
.manuel-hero-icon {
    -fx-font-size: 28px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 1, 0, 0, 1);
    -fx-font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}
.admin-hero-icon {
    -fx-font-size: 28px;
    -fx-text-fill: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 1, 0, 0, 1);
    -fx-font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}