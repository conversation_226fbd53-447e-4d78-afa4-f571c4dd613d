/* Styles pour la boîte de dialogue d'édition d'employé */

.dialog-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 15 0;
}

.dialog-content {
    -fx-padding: 10 0;
    -fx-min-width: 400px;
}

.field-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #34495e;
}

.current-value {
    -fx-font-size: 14px;
    -fx-text-fill: #2980b9;
    -fx-font-weight: bold;
    -fx-padding: 5 10;
    -fx-background-color: #ecf0f1;
    -fx-background-radius: 4px;
}

.help-text {
    -fx-font-size: 10px;
    -fx-text-fill: #7f8c8d;
    -fx-font-style: italic;
}

.error-message {
    -fx-text-fill: #e74c3c;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.dialog-buttons {
    -fx-alignment: center-right;
    -fx-padding: 15 0 0 0;
}

.primary-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 20;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.primary-button:hover {
    -fx-background-color: #2980b9;
}

.secondary-button {
    -fx-background-color: #95a5a6;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 20;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: #7f8c8d;
}

TextField, PasswordField {
    -fx-padding: 8 12;
    -fx-background-radius: 4px;
    -fx-border-color: #bdc3c7;
    -fx-border-radius: 4px;
    -fx-font-size: 12px;
}

TextField:focused, PasswordField:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
}
